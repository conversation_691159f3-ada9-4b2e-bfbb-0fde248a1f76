name: Create production build and submit

on:
  push:
    tags:
      - 'v*'

jobs:
  build_ios:
    name: Build iOS Production
    type: build
    params:
      platform: ios
      profile: production

  build_android:
    name: Build Android Production
    type: build
    params:
      platform: android
      profile: production

  submit_ios:
    name: Submit iOS to App Store
    needs: [build_ios]
    type: submit
    params:
      build_id: ${{ needs.build_ios.outputs.build_id }}
      profile: production

  submit_android:
    name: Submit Android to Play Store
    needs: [build_android]
    type: submit
    params:
      build_id: ${{ needs.build_android.outputs.build_id }}
      profile: production
