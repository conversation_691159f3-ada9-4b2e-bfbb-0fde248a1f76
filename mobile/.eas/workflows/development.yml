name: Create development builds

on:
  push:
    branches: [develop]
    paths:
      - 'mobile/**'
  pull_request_labeled:
    - development-build

jobs:
  test:
    name: Run Tests
    steps:
      - uses: eas/checkout
      - uses: eas/install_node_modules
      - name: Run TypeScript check
        run: npx tsc --noEmit
      - name: Run ESLint
        run: npx eslint . --ext .ts,.tsx,.js,.jsx
      - name: Run tests
        run: npm test

  build_ios:
    name: Build iOS Development
    needs: [test]
    type: build
    params:
      platform: ios
      profile: development

  build_android:
    name: Build Android Development
    needs: [test]
    type: build
    params:
      platform: android
      profile: development
