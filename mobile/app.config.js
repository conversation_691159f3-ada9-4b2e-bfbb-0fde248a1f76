const IS_DEV = process.env.APP_VARIANT === "development";
const IS_PREVIEW = process.env.APP_VARIANT === "preview";

const getUniqueIdentifier = () => {
  if (IS_DEV) {
    return "com.aiplanmytrip.tripitineraryplanner.dev";
  }

  if (IS_PREVIEW) {
    return "com.aiplanmytrip.tripitineraryplanner.preview";
  }

  return "com.aiplanmytrip.tripitineraryplanner";
};

const getAppName = () => {
  if (IS_DEV) {
    return "AiPlanMyTrip (Dev)";
  }

  if (IS_PREVIEW) {
    return "AiPlanMyTrip (Preview)";
  }

  return "AiPlanMyTrip";
};

const iosUrlScheme = () => {
  if (IS_DEV) {
    return "com.googleusercontent.apps.994695515433-9ulrkcdr7o8au00b7cchq5o3vrq1bqq3";
  }

  if (IS_PREVIEW) {
    return "com.googleusercontent.apps.994695515433-5g1frp50vn5p56l604hnpf6c9pj4r393";
  }

  return "com.googleusercontent.apps.994695515433-tqti7rlm7h5s2kmkpb8d3ja765jnn76f";
}

export default ({ config }) => ({
  ...config,
  name: getAppName(),
  slug: "aiplanmytrip",
  version: "1.0.0",
  orientation: "portrait",
  icon: "./assets/images/logo.png",
  scheme: getUniqueIdentifier(),
  userInterfaceStyle: "automatic",
  newArchEnabled: true,
  ios: {
    supportsTablet: true,
    bundleIdentifier: getUniqueIdentifier(),
    infoPlist: {
      ITSAppUsesNonExemptEncryption: false,
    },
  },
  android: {
    adaptiveIcon: {
      foregroundImage: "./assets/images/adaptive-icon.png",
      backgroundColor: "#ffffff",
    },
    package: getUniqueIdentifier(),
    edgeToEdgeEnabled: true,
  },
  web: {
    bundler: "metro",
    output: "server",
    favicon: "./assets/images/favicon.png",
  },
  plugins: [
    [
      "@react-native-google-signin/google-signin",
      {
        iosUrlScheme: iosUrlScheme(),
      },
    ],
    [
      "@rnmapbox/maps",
      {
        RNMapboxMapsDownloadToken: "*****************************************************************************************",
      },
    ],
    [
      "react-native-iap",
      {
        paymentProvider: "both",
      },
    ],
    [
      "expo-router",
      {
        origin: "https://aiplanmytrip.com/",
      },
    ],
    [
      "expo-splash-screen",
      {
        image: "./assets/images/splash-icon.png",
        imageWidth: 200,
        resizeMode: "contain",
        backgroundColor: "#2196F3",
        dark: {
          backgroundColor: "#2196F3",
          image: "./assets/images/splash-icon.png",
        }
      },
    ],
    [
      "expo-notifications",
      {
        icon: "./assets/images/icon.png",
        color: "#ffffff",
        sounds: ["./assets/sounds/notification.mp3"],
      },
    ],
  ],
  experiments: {
    typedRoutes: true,
  },
  "extra": {
    "eas": {
      "projectId": "a2af2661-912a-45c1-a6ca-4bd74ac3f2b3"
    }
  },
  "updates": {
    "url": "https://u.expo.dev/a2af2661-912a-45c1-a6ca-4bd74ac3f2b3"
  },
  runtimeVersion: {
    policy: "appVersion",
  },
});
